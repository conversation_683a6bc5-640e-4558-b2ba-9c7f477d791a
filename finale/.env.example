# Database Configuration
# Copy this file to .env and update with your actual database credentials

DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password

# Finale API Configuration
# Configure these based on your Finale API access method

# Option 1: API Key Authentication (recommended)
FINALE_API_KEY=your_api_key_here

# Option 2: Username/Password Authentication
FINALE_USERNAME=your_finale_username
FINALE_PASSWORD=your_finale_password

# Optional: Tenant ID (if required by your Finale setup)
FINALE_TENANT_ID=your_tenant_id

# Optional: API timeout in seconds (default: 30)
FINALE_TIMEOUT=30
