#!/usr/bin/env python3
"""
Finale Supplier Data Migration Script

This script migrates supplier data from Finale API to the local database system.
It handles the conversion of Finale's party data structure to the vendor table format.

Author: Migration Script
Date: 2025-01-17
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os
import requests
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class FinaleAPIClient:
    """Finale API client for fetching supplier data"""

    def __init__(self):
        self.base_url = "https://app.finaleinventory.com"
        self.api_endpoint = "/mercaso/api/partygroup"

        # API credentials from environment variables
        self.api_key = 'Basic Mk5aWWs3MU1mcERvOlpDdWFJREFvNmNIWWV5RERiQ05uZ000cE9CVDdJaWNj'

        # Request timeout settings
        self.timeout = int(os.getenv('FINALE_TIMEOUT', '30'))

        # Session for connection reuse
        self.session = requests.Session()
        self._setup_session()

    def _setup_session(self):
        """Setup session with default headers and authentication"""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Finale-Migration-Script/1.0'
        }

        # Add API key if available
        if self.api_key:
            headers['Authorization'] = f'Basic {self.api_key}'

        self.session.headers.update(headers)


    def fetch_supplier_data(self, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Fetch supplier data from Finale API

        Args:
            params: Optional query parameters for the API call

        Returns:
            Supplier data from Finale API or None if failed
        """
        url = f"{self.base_url}{self.api_endpoint}"

        try:
            logging.info(f"Fetching supplier data from Finale API: {url}")

            # Default parameters
            default_params = {
                'limit': 1000,  # Adjust as needed
                'offset': 0
            }

            if params:
                default_params.update(params)

            response = self.session.get(
                url,
                params=default_params,
                timeout=self.timeout
            )

            # Check response status
            response.raise_for_status()

            # Parse JSON response
            data = response.json()

            logging.info(f"Successfully fetched supplier data. Records: {len(data.get('partyId', []))}")
            return data

        except requests.exceptions.RequestException as e:
            logging.error(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logging.error(f"Response status: {e.response.status_code}")
                logging.error(f"Response content: {e.response.text}")
            return None
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error fetching supplier data: {e}")
            return None

    def fetch_all_suppliers(self, batch_size: int = 1000) -> Optional[Dict[str, Any]]:
        """
        Fetch all suppliers using pagination

        Args:
            batch_size: Number of records to fetch per request

        Returns:
            Combined supplier data from all pages
        """
        all_data = {
            'partyId': [],
            'partyUrl': [],
            'statusId': [],
            'lastUpdatedDate': [],
            'createdDate': [],
            'roleTypeIdList': [],
            'glAccountList': [],
            'contentList': [],
            'userFieldDataList': [],
            'connectionRelationUrlList': [],
            'productStoreUrlList': []
        }

        offset = 0
        total_fetched = 0

        while True:
            params = {
                'limit': batch_size,
                'offset': offset
            }

            batch_data = self.fetch_supplier_data(params)

            if not batch_data:
                logging.error(f"Failed to fetch batch at offset {offset}")
                break

            # Check if we got any data
            party_ids = batch_data.get('partyId', [])
            if not party_ids:
                logging.info("No more data to fetch")
                break

            # Merge batch data into all_data
            for key in all_data.keys():
                if key in batch_data:
                    all_data[key].extend(batch_data[key])

            batch_count = len(party_ids)
            total_fetched += batch_count
            logging.info(f"Fetched batch: {batch_count} records (total: {total_fetched})")

            # If batch is smaller than batch_size, we've reached the end
            if batch_count < batch_size:
                break

            offset += batch_size

        if total_fetched > 0:
            logging.info(f"Successfully fetched all supplier data. Total records: {total_fetched}")
            return all_data
        else:
            logging.error("No supplier data fetched")
            return None

    def close(self):
        """Close the session"""
        if self.session:
            self.session.close()


class DatabaseConfig:
    """Database configuration class"""

    def __init__(self):
        # Database connection parameters
        # You can modify these or use environment variables
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'your_database')
        self.username = os.getenv('DB_USER', 'your_username')
        self.password = os.getenv('DB_PASSWORD', 'your_password')

    def get_connection_string(self) -> str:
        """Get database connection string"""
        return f"host={self.host} port={self.port} dbname={self.database} user={self.username} password={self.password}"


class FinaleDataProcessor:
    """Process Finale API data for migration"""

    @staticmethod
    def convert_status(finale_status: str) -> str:
        """Convert Finale status to system status"""
        status_mapping = {
            'PARTY_ENABLED': 'ACTIVE',
            'PARTY_DISABLED': 'DRAFT'
        }
        return status_mapping.get(finale_status, 'UNKNOWN')

    @staticmethod
    def parse_datetime(date_string: str) -> Optional[datetime]:
        """Parse Finale datetime string to Python datetime"""
        try:
            # Finale format: "2022-09-20T22:08:08"
            return datetime.fromisoformat(date_string)
        except (ValueError, TypeError) as e:
            logging.warning(f"Failed to parse datetime '{date_string}': {e}")
            return None

    @staticmethod
    def generate_vendor_name(party_id: str) -> str:
        """Generate vendor name from party ID"""
        return f"Finale Vendor {party_id}"

    def process_finale_data(self, finale_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process Finale API data and convert to vendor records with related data

        Args:
            finale_data: Raw data from Finale API

        Returns:
            List of processed vendor records with related data (addresses, emails, phones)
        """
        processed_vendors = []

        # Get the length of data arrays
        party_ids = finale_data.get('partyId', [])
        party_urls = finale_data.get('partyUrl', [])
        status_ids = finale_data.get('statusId', [])
        last_updated_dates = finale_data.get('lastUpdatedDate', [])
        created_dates = finale_data.get('createdDate', [])
        content_lists = finale_data.get('contentList', [])

        # Ensure all arrays have the same length
        data_length = len(party_ids)
        if not all(len(arr) == data_length for arr in [party_urls, status_ids, last_updated_dates, created_dates]):
            logging.error("Data arrays have inconsistent lengths")
            return []

        # Process each vendor record
        for i in range(data_length):
            try:
                vendor_id = str(uuid.uuid4())  # Generate new UUID

                vendor_record = {
                    'id': vendor_id,
                    'finale_id': party_ids[i],
                    'vendor_name': self.generate_vendor_name(party_ids[i]),
                    'vendor_contact_name': None,  # Will be extracted from content if available
                    'vendor_contact_tel': None,   # Will be extracted from content if available
                    'vendor_company_name': self.generate_vendor_name(party_ids[i]),
                    'status': self.convert_status(status_ids[i]),
                    'created_at': self.parse_datetime(created_dates[i]),
                    'created_by': 'FINALE_MIGRATION',
                    'updated_at': self.parse_datetime(last_updated_dates[i]),
                    'updated_by': 'FINALE_MIGRATION',
                    'deleted_at': None,
                    'deleted_by': None,
                    'created_user_name': 'Finale Migration Script',
                    'updated_user_name': 'Finale Migration Script',
                    'deleted_user_name': None,
                    'external_picking': False,  # Default value
                    'shutdown_window_enabled': False,  # Default value
                    'shutdown_window_start': '05:00:00',  # Default value
                    'shutdown_window_end': '06:00:00',    # Default value
                    'shutdown_window_days': 'FRIDAY,SATURDAY',  # Default value
                    'default_terms': None,
                    'default_lead_days': None,
                    'notes': f'Migrated from Finale. Original Party URL: {party_urls[i] if i < len(party_urls) else "N/A"}',

                    # Related data
                    'addresses': [],
                    'emails': [],
                    'phones': []
                }

                # Process content data if available
                if i < len(content_lists) and content_lists[i]:
                    self.process_content_data(vendor_record, content_lists[i])

                processed_vendors.append(vendor_record)
                logging.info(f"Processed vendor record for party ID: {party_ids[i]}")

            except Exception as e:
                logging.error(f"Error processing vendor record at index {i}: {e}")
                continue

        return processed_vendors

    def process_content_data(self, vendor_record: Dict[str, Any], content_data: List[Dict[str, Any]]):
        """
        Process content data to extract addresses, emails, and phone numbers

        Args:
            vendor_record: The vendor record to update
            content_data: Content data from Finale API
        """
        vendor_id = vendor_record['id']
        created_at = vendor_record['created_at']

        for content_item in content_data:
            try:
                content_type = content_item.get('type', '').lower()

                if content_type == 'address':
                    address_data = self.extract_address_data(vendor_id, content_item, created_at)
                    if address_data:
                        vendor_record['addresses'].append(address_data)

                elif content_type == 'email':
                    email_data = self.extract_email_data(vendor_id, content_item, created_at)
                    if email_data:
                        vendor_record['emails'].append(email_data)

                elif content_type == 'phone':
                    phone_data = self.extract_phone_data(vendor_id, content_item, created_at)
                    if phone_data:
                        vendor_record['phones'].append(phone_data)

                elif content_type == 'contact':
                    # Extract contact name and phone from contact data
                    contact_name = content_item.get('name')
                    contact_phone = content_item.get('phone')

                    if contact_name and not vendor_record['vendor_contact_name']:
                        vendor_record['vendor_contact_name'] = contact_name

                    if contact_phone and not vendor_record['vendor_contact_tel']:
                        vendor_record['vendor_contact_tel'] = contact_phone

            except Exception as e:
                logging.warning(f"Error processing content item: {e}")
                continue

    def extract_address_data(self, vendor_id: str, content_item: Dict[str, Any], created_at: datetime) -> Optional[Dict[str, Any]]:
        """Extract address data from content item"""
        try:
            street_address = content_item.get('street_address') or content_item.get('address') or content_item.get('street')
            city = content_item.get('city')

            # Address must have at least street and city
            if not street_address or not city:
                return None

            # Ensure minimum length requirements
            if len(street_address) < 5 or len(city) < 2:
                return None

            return {
                'entity_type': 'VENDOR',
                'entity_id': vendor_id,
                'street_address': street_address[:500],  # Truncate if too long
                'city': city[:100],
                'state': (content_item.get('state') or '')[:100],
                'postal_code': (content_item.get('postal_code') or content_item.get('zip') or '')[:20],
                'country': (content_item.get('country') or 'US')[:100],
                'directions': (content_item.get('directions') or '')[:1000],
                'purpose': content_item.get('purpose', 'BUSINESS'),
                'additional_lines': (content_item.get('additional_lines') or '')[:1000],
                'created_at': created_at,
                'created_by': 'FINALE_MIGRATION',
                'created_user_name': 'Finale Migration Script'
            }
        except Exception as e:
            logging.warning(f"Error extracting address data: {e}")
            return None

    def extract_email_data(self, vendor_id: str, content_item: Dict[str, Any], created_at: datetime) -> Optional[Dict[str, Any]]:
        """Extract email data from content item"""
        try:
            email = content_item.get('email') or content_item.get('email_address')

            if not email or len(email) < 5:
                return None

            # Basic email validation
            if '@' not in email or '.' not in email:
                return None

            return {
                'entity_type': 'VENDOR',
                'entity_id': vendor_id,
                'email_type': content_item.get('email_type', 'BUSINESS'),
                'email': email[:255],
                'extension': (content_item.get('extension') or '')[:100],
                'created_at': created_at,
                'created_by': 'FINALE_MIGRATION',
                'created_user_name': 'Finale Migration Script'
            }
        except Exception as e:
            logging.warning(f"Error extracting email data: {e}")
            return None

    def extract_phone_data(self, vendor_id: str, content_item: Dict[str, Any], created_at: datetime) -> Optional[Dict[str, Any]]:
        """Extract phone data from content item"""
        try:
            phone = content_item.get('phone') or content_item.get('phone_number')

            if not phone:
                return None

            # Clean phone number (remove non-digits except +)
            cleaned_phone = ''.join(c for c in phone if c.isdigit() or c == '+')

            if len(cleaned_phone) < 7:
                return None

            return {
                'entity_type': 'VENDOR',
                'entity_id': vendor_id,
                'phone_type': content_item.get('phone_type', 'BUSINESS'),
                'phone_number': cleaned_phone[:20],
                'extension': (content_item.get('extension') or '')[:10],
                'created_at': created_at,
                'created_by': 'FINALE_MIGRATION',
                'created_user_name': 'Finale Migration Script'
            }
        except Exception as e:
            logging.warning(f"Error extracting phone data: {e}")
            return None


class VendorMigrator:
    """Handle vendor data migration to database"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.connection = None

    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(
                self.db_config.get_connection_string(),
                cursor_factory=RealDictCursor
            )
            self.connection.autocommit = False
            logging.info("Database connection established successfully")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to database: {e}")
            return False

    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logging.info("Database connection closed")

    def get_existing_vendor(self, finale_id: str) -> Optional[Dict[str, Any]]:
        """Get existing vendor record by finale_id"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM vendor WHERE finale_id = %s AND deleted_at IS NULL",
                    (finale_id,)
                )
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error checking existing vendor {finale_id}: {e}")
            return None

    def insert_vendor(self, vendor_data: Dict[str, Any]) -> bool:
        """Insert vendor record into database"""
        try:
            insert_query = """
                INSERT INTO vendor (
                    id, vendor_name, vendor_contact_name, vendor_contact_tel,
                    vendor_company_name, status, created_at, created_by,
                    updated_at, updated_by, deleted_at, deleted_by,
                    created_user_name, updated_user_name, deleted_user_name,
                    external_picking, finale_id, shutdown_window_enabled,
                    shutdown_window_start, shutdown_window_end, shutdown_window_days,
                    default_terms, default_lead_days, notes
                ) VALUES (
                    %(id)s, %(vendor_name)s, %(vendor_contact_name)s, %(vendor_contact_tel)s,
                    %(vendor_company_name)s, %(status)s, %(created_at)s, %(created_by)s,
                    %(updated_at)s, %(updated_by)s, %(deleted_at)s, %(deleted_by)s,
                    %(created_user_name)s, %(updated_user_name)s, %(deleted_user_name)s,
                    %(external_picking)s, %(finale_id)s, %(shutdown_window_enabled)s,
                    %(shutdown_window_start)s, %(shutdown_window_end)s, %(shutdown_window_days)s,
                    %(default_terms)s, %(default_lead_days)s, %(notes)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, vendor_data)
                logging.info(f"Inserted vendor: {vendor_data['finale_id']}")
                return True

        except Exception as e:
            logging.error(f"Error inserting vendor {vendor_data.get('finale_id', 'Unknown')}: {e}")
            return False

    def update_vendor(self, vendor_data: Dict[str, Any]) -> bool:
        """Update existing vendor record in database"""
        try:
            update_query = """
                UPDATE vendor SET
                    vendor_name = %(vendor_name)s,
                    vendor_contact_name = %(vendor_contact_name)s,
                    vendor_contact_tel = %(vendor_contact_tel)s,
                    vendor_company_name = %(vendor_company_name)s,
                    status = %(status)s,
                    updated_at = %(updated_at)s,
                    updated_by = %(updated_by)s,
                    updated_user_name = %(updated_user_name)s,
                    external_picking = %(external_picking)s,
                    shutdown_window_enabled = %(shutdown_window_enabled)s,
                    shutdown_window_start = %(shutdown_window_start)s,
                    shutdown_window_end = %(shutdown_window_end)s,
                    shutdown_window_days = %(shutdown_window_days)s,
                    default_terms = %(default_terms)s,
                    default_lead_days = %(default_lead_days)s,
                    notes = %(notes)s
                WHERE finale_id = %(finale_id)s AND deleted_at IS NULL
            """

            with self.connection.cursor() as cursor:
                cursor.execute(update_query, vendor_data)
                if cursor.rowcount > 0:
                    logging.info(f"Updated vendor: {vendor_data['finale_id']}")
                    return True
                else:
                    logging.warning(f"No rows updated for vendor: {vendor_data['finale_id']}")
                    return False

        except Exception as e:
            logging.error(f"Error updating vendor {vendor_data.get('finale_id', 'Unknown')}: {e}")
            return False

    def insert_address(self, vendor_id: str, address_data: Dict[str, Any]) -> bool:
        """Insert address record for vendor"""
        try:
            insert_query = """
                INSERT INTO address (
                    entity_type, entity_id, street_address, city, state,
                    postal_code, country, directions, purpose, additional_lines,
                    created_at, created_by, created_user_name
                ) VALUES (
                    %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                    %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                    %(created_at)s, %(created_by)s, %(created_user_name)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, address_data)
                logging.info(f"Inserted address for vendor: {vendor_id}")
                return True

        except Exception as e:
            logging.error(f"Error inserting address for vendor {vendor_id}: {e}")
            return False

    def insert_email(self, vendor_id: str, email_data: Dict[str, Any]) -> bool:
        """Insert email record for vendor"""
        try:
            insert_query = """
                INSERT INTO email (
                    entity_type, entity_id, email_type, email, extension,
                    created_at, created_by, created_user_name
                ) VALUES (
                    %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                    %(created_at)s, %(created_by)s, %(created_user_name)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, email_data)
                logging.info(f"Inserted email for vendor: {vendor_id}")
                return True

        except Exception as e:
            logging.error(f"Error inserting email for vendor {vendor_id}: {e}")
            return False

    def insert_phone(self, vendor_id: str, phone_data: Dict[str, Any]) -> bool:
        """Insert phone number record for vendor"""
        try:
            insert_query = """
                INSERT INTO phone_number (
                    entity_type, entity_id, phone_type, phone_number, extension,
                    created_at, created_by, created_user_name
                ) VALUES (
                    %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                    %(created_at)s, %(created_by)s, %(created_user_name)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, phone_data)
                logging.info(f"Inserted phone for vendor: {vendor_id}")
                return True

        except Exception as e:
            logging.error(f"Error inserting phone for vendor {vendor_id}: {e}")
            return False

    def delete_vendor_related_data(self, vendor_id: str) -> bool:
        """Delete existing address, email, and phone data for vendor before inserting new ones"""
        try:
            with self.connection.cursor() as cursor:
                # Delete existing addresses
                cursor.execute(
                    "DELETE FROM address WHERE entity_type = 'VENDOR' AND entity_id = %s",
                    (vendor_id,)
                )

                # Delete existing emails
                cursor.execute(
                    "DELETE FROM email WHERE entity_type = 'VENDOR' AND entity_id = %s",
                    (vendor_id,)
                )

                # Delete existing phone numbers
                cursor.execute(
                    "DELETE FROM phone_number WHERE entity_type = 'VENDOR' AND entity_id = %s",
                    (vendor_id,)
                )

                logging.info(f"Deleted existing related data for vendor: {vendor_id}")
                return True

        except Exception as e:
            logging.error(f"Error deleting related data for vendor {vendor_id}: {e}")
            return False

    def update_vendor_related_data(self, vendor_id: str, vendor_record: Dict[str, Any]) -> bool:
        """Update addresses, emails, and phone numbers for a vendor"""
        try:
            # Delete existing related data first
            if not self.delete_vendor_related_data(vendor_id):
                logging.warning(f"Failed to delete existing related data for vendor {vendor_id}")

            success_count = 0
            total_count = 0

            # Insert addresses
            addresses = vendor_record.get('addresses', [])
            for address_data in addresses:
                address_data['entity_id'] = vendor_id  # Use existing vendor ID
                total_count += 1
                if self.insert_address(vendor_id, address_data):
                    success_count += 1

            # Insert emails
            emails = vendor_record.get('emails', [])
            for email_data in emails:
                email_data['entity_id'] = vendor_id  # Use existing vendor ID
                total_count += 1
                if self.insert_email(vendor_id, email_data):
                    success_count += 1

            # Insert phone numbers
            phones = vendor_record.get('phones', [])
            for phone_data in phones:
                phone_data['entity_id'] = vendor_id  # Use existing vendor ID
                total_count += 1
                if self.insert_phone(vendor_id, phone_data):
                    success_count += 1

            if total_count > 0:
                logging.info(f"Updated related data for vendor {vendor_id}: {success_count}/{total_count} successful")

            return success_count == total_count

        except Exception as e:
            logging.error(f"Error updating related data for vendor {vendor_id}: {e}")
            return False

    def migrate_vendors(self, vendor_records: List[Dict[str, Any]], update_existing: bool = True) -> Dict[str, int]:
        """
        Migrate vendor records to database

        Args:
            vendor_records: List of processed vendor records
            update_existing: Whether to update existing vendors (True) or skip them (False)

        Returns:
            Dictionary with migration statistics
        """
        stats = {
            'total': len(vendor_records),
            'inserted': 0,
            'updated': 0,
            'skipped': 0,
            'failed': 0,
            'addresses_processed': 0,
            'emails_processed': 0,
            'phones_processed': 0
        }

        try:
            for vendor_record in vendor_records:
                finale_id = vendor_record.get('finale_id')

                # Count related data for statistics
                stats['addresses_processed'] += len(vendor_record.get('addresses', []))
                stats['emails_processed'] += len(vendor_record.get('emails', []))
                stats['phones_processed'] += len(vendor_record.get('phones', []))

                # Check if vendor already exists
                existing_vendor = self.get_existing_vendor(finale_id)

                if existing_vendor:
                    if update_existing:
                        # Update existing vendor
                        if self.update_vendor(vendor_record):
                            # Update related data (addresses, emails, phones)
                            vendor_id = existing_vendor['id']  # Use existing vendor ID
                            self.update_vendor_related_data(vendor_id, vendor_record)
                            stats['updated'] += 1
                        else:
                            stats['failed'] += 1
                    else:
                        logging.info(f"Vendor {finale_id} already exists, skipping")
                        stats['skipped'] += 1
                else:
                    # Vendor doesn't exist, skip it (as per requirement)
                    logging.info(f"Vendor {finale_id} not found in database, skipping")
                    stats['skipped'] += 1

            # Commit transaction
            self.connection.commit()
            logging.info("Migration transaction committed successfully")

        except Exception as e:
            logging.error(f"Error during migration: {e}")
            self.connection.rollback()
            logging.info("Migration transaction rolled back")

        return stats


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('finale_migration.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_finale_data(file_path: str = None, json_data: str = None) -> Optional[Dict[str, Any]]:
    """
    Load Finale data from file or JSON string

    Args:
        file_path: Path to JSON file containing Finale data
        json_data: JSON string containing Finale data

    Returns:
        Parsed Finale data or None if failed
    """
    try:
        if file_path:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif json_data:
            return json.loads(json_data)
        else:
            logging.error("Either file_path or json_data must be provided")
            return None
    except Exception as e:
        logging.error(f"Error loading Finale data: {e}")
        return None


def main():
    """Main migration function"""
    setup_logging()
    logging.info("Starting Finale supplier data migration")

    api_client = None
    migrator = None

    try:
        # Initialize components
        api_client = FinaleAPIClient()
        db_config = DatabaseConfig()
        data_processor = FinaleDataProcessor()
        migrator = VendorMigrator(db_config)

        # Connect to database
        if not migrator.connect():
            logging.error("Failed to connect to database. Exiting.")
            return False

        # Fetch data from Finale API
        logging.info("Fetching supplier data from Finale API...")
        finale_data = api_client.fetch_all_suppliers()

        if not finale_data:
            logging.error("Failed to fetch data from Finale API. Exiting.")
            return False

        logging.info(f"Successfully fetched {len(finale_data.get('partyId', []))} supplier records from API")

        # Process the data
        processed_vendors = data_processor.process_finale_data(finale_data)

        if not processed_vendors:
            logging.error("No vendor records processed. Exiting.")
            return False

        logging.info(f"Processed {len(processed_vendors)} vendor records")

        # Migrate to database
        migration_stats = migrator.migrate_vendors(processed_vendors)

        # Log migration results
        logging.info("Migration completed!")
        logging.info(f"Total records: {migration_stats['total']}")
        logging.info(f"Successfully updated: {migration_stats['updated']}")
        logging.info(f"Skipped (not found): {migration_stats['skipped']}")
        logging.info(f"Failed: {migration_stats['failed']}")
        logging.info(f"Addresses processed: {migration_stats['addresses_processed']}")
        logging.info(f"Emails processed: {migration_stats['emails_processed']}")
        logging.info(f"Phone numbers processed: {migration_stats['phones_processed']}")

        return True

    except Exception as e:
        logging.error(f"Unexpected error during migration: {e}")
        return False

    finally:
        # Clean up
        if api_client:
            api_client.close()
        if migrator:
            migrator.disconnect()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)