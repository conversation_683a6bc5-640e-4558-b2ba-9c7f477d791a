#!/usr/bin/env python3
"""
Finale Supplier Data Migration Script

This script migrates supplier data from Finale API to the local database system.
It handles the conversion of Finale's party data structure to the vendor table format.

Author: Migration Script
Date: 2025-01-17
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseConfig:
    """Database configuration class"""

    def __init__(self):
        # Database connection parameters
        # You can modify these or use environment variables
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'your_database')
        self.username = os.getenv('DB_USER', 'your_username')
        self.password = os.getenv('DB_PASSWORD', 'your_password')

    def get_connection_string(self) -> str:
        """Get database connection string"""
        return f"host={self.host} port={self.port} dbname={self.database} user={self.username} password={self.password}"


class FinaleDataProcessor:
    """Process Finale API data for migration"""

    @staticmethod
    def convert_status(finale_status: str) -> str:
        """Convert Finale status to system status"""
        status_mapping = {
            'PARTY_ENABLED': 'ACTIVE',
            'PARTY_DISABLED': 'INACTIVE',
            'PARTY_SUSPENDED': 'SUSPENDED'
        }
        return status_mapping.get(finale_status, 'UNKNOWN')

    @staticmethod
    def parse_datetime(date_string: str) -> Optional[datetime]:
        """Parse Finale datetime string to Python datetime"""
        try:
            # Finale format: "2022-09-20T22:08:08"
            return datetime.fromisoformat(date_string)
        except (ValueError, TypeError) as e:
            logging.warning(f"Failed to parse datetime '{date_string}': {e}")
            return None

    @staticmethod
    def generate_vendor_name(party_id: str) -> str:
        """Generate vendor name from party ID"""
        return f"Finale Vendor {party_id}"

    def process_finale_data(self, finale_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process Finale API data and convert to vendor records

        Args:
            finale_data: Raw data from Finale API

        Returns:
            List of processed vendor records
        """
        processed_vendors = []

        # Get the length of data arrays
        party_ids = finale_data.get('partyId', [])
        party_urls = finale_data.get('partyUrl', [])
        status_ids = finale_data.get('statusId', [])
        last_updated_dates = finale_data.get('lastUpdatedDate', [])
        created_dates = finale_data.get('createdDate', [])

        # Ensure all arrays have the same length
        data_length = len(party_ids)
        if not all(len(arr) == data_length for arr in [party_urls, status_ids, last_updated_dates, created_dates]):
            logging.error("Data arrays have inconsistent lengths")
            return []

        # Process each vendor record
        for i in range(data_length):
            try:
                vendor_record = {
                    'id': str(uuid.uuid4()),  # Generate new UUID
                    'finale_id': party_ids[i],
                    'vendor_name': self.generate_vendor_name(party_ids[i]),
                    'vendor_contact_name': None,  # Not available in Finale data
                    'vendor_contact_tel': None,   # Not available in Finale data
                    'vendor_company_name': self.generate_vendor_name(party_ids[i]),
                    'status': self.convert_status(status_ids[i]),
                    'created_at': self.parse_datetime(created_dates[i]),
                    'created_by': 'FINALE_MIGRATION',
                    'updated_at': self.parse_datetime(last_updated_dates[i]),
                    'updated_by': 'FINALE_MIGRATION',
                    'deleted_at': None,
                    'deleted_by': None,
                    'created_user_name': 'Finale Migration Script',
                    'updated_user_name': 'Finale Migration Script',
                    'deleted_user_name': None,
                    'external_picking': False,  # Default value
                    'shutdown_window_enabled': False,  # Default value
                    'shutdown_window_start': '05:00:00',  # Default value
                    'shutdown_window_end': '06:00:00',    # Default value
                    'shutdown_window_days': 'FRIDAY,SATURDAY',  # Default value
                    'default_terms': None,
                    'default_lead_days': None,
                    'notes': f'Migrated from Finale. Original Party URL: {party_urls[i] if i < len(party_urls) else "N/A"}'
                }

                processed_vendors.append(vendor_record)
                logging.info(f"Processed vendor record for party ID: {party_ids[i]}")

            except Exception as e:
                logging.error(f"Error processing vendor record at index {i}: {e}")
                continue

        return processed_vendors


class VendorMigrator:
    """Handle vendor data migration to database"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.connection = None

    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(
                self.db_config.get_connection_string(),
                cursor_factory=RealDictCursor
            )
            self.connection.autocommit = False
            logging.info("Database connection established successfully")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to database: {e}")
            return False

    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logging.info("Database connection closed")

    def check_existing_vendor(self, finale_id: str) -> bool:
        """Check if vendor with finale_id already exists"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    "SELECT id FROM vendor WHERE finale_id = %s AND deleted_at IS NULL",
                    (finale_id,)
                )
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logging.error(f"Error checking existing vendor {finale_id}: {e}")
            return False

    def insert_vendor(self, vendor_data: Dict[str, Any]) -> bool:
        """Insert vendor record into database"""
        try:
            insert_query = """
                INSERT INTO vendor (
                    id, vendor_name, vendor_contact_name, vendor_contact_tel,
                    vendor_company_name, status, created_at, created_by,
                    updated_at, updated_by, deleted_at, deleted_by,
                    created_user_name, updated_user_name, deleted_user_name,
                    external_picking, finale_id, shutdown_window_enabled,
                    shutdown_window_start, shutdown_window_end, shutdown_window_days,
                    default_terms, default_lead_days, notes
                ) VALUES (
                    %(id)s, %(vendor_name)s, %(vendor_contact_name)s, %(vendor_contact_tel)s,
                    %(vendor_company_name)s, %(status)s, %(created_at)s, %(created_by)s,
                    %(updated_at)s, %(updated_by)s, %(deleted_at)s, %(deleted_by)s,
                    %(created_user_name)s, %(updated_user_name)s, %(deleted_user_name)s,
                    %(external_picking)s, %(finale_id)s, %(shutdown_window_enabled)s,
                    %(shutdown_window_start)s, %(shutdown_window_end)s, %(shutdown_window_days)s,
                    %(default_terms)s, %(default_lead_days)s, %(notes)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, vendor_data)
                logging.info(f"Inserted vendor: {vendor_data['finale_id']}")
                return True

        except Exception as e:
            logging.error(f"Error inserting vendor {vendor_data.get('finale_id', 'Unknown')}: {e}")
            return False

    def migrate_vendors(self, vendor_records: List[Dict[str, Any]], skip_existing: bool = True) -> Dict[str, int]:
        """
        Migrate vendor records to database

        Args:
            vendor_records: List of processed vendor records
            skip_existing: Whether to skip existing vendors

        Returns:
            Dictionary with migration statistics
        """
        stats = {
            'total': len(vendor_records),
            'inserted': 0,
            'skipped': 0,
            'failed': 0
        }

        try:
            for vendor_record in vendor_records:
                finale_id = vendor_record.get('finale_id')

                # Check if vendor already exists
                if skip_existing and self.check_existing_vendor(finale_id):
                    logging.info(f"Vendor {finale_id} already exists, skipping")
                    stats['skipped'] += 1
                    continue

                # Insert vendor record
                if self.insert_vendor(vendor_record):
                    stats['inserted'] += 1
                else:
                    stats['failed'] += 1

            # Commit transaction
            self.connection.commit()
            logging.info("Migration transaction committed successfully")

        except Exception as e:
            logging.error(f"Error during migration: {e}")
            self.connection.rollback()
            logging.info("Migration transaction rolled back")

        return stats


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('finale_migration.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_finale_data(file_path: str = None, json_data: str = None) -> Optional[Dict[str, Any]]:
    """
    Load Finale data from file or JSON string

    Args:
        file_path: Path to JSON file containing Finale data
        json_data: JSON string containing Finale data

    Returns:
        Parsed Finale data or None if failed
    """
    try:
        if file_path:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif json_data:
            return json.loads(json_data)
        else:
            logging.error("Either file_path or json_data must be provided")
            return None
    except Exception as e:
        logging.error(f"Error loading Finale data: {e}")
        return None


def main():
    """Main migration function"""
    setup_logging()
    logging.info("Starting Finale supplier data migration")

    # Sample Finale data (replace with actual data source)
    sample_finale_data = {
        "partyId": [
            "100001",
            "100002",
            "100017",
            "100026",
            "100033"
        ],
        "partyUrl": [
            "/mercaso/api/partygroup/100001",
            "/mercaso/api/partygroup/100002",
            "/mercaso/api/partygroup/100017",
            "/mercaso/api/partygroup/100026",
            "/mercaso/api/partygroup/100033"
        ],
        "statusId": [
            "PARTY_ENABLED",
            "PARTY_ENABLED",
            "PARTY_ENABLED",
            "PARTY_ENABLED",
            "PARTY_ENABLED"
        ],
        "lastUpdatedDate": [
            "2022-09-20T22:08:08",
            "2022-09-20T22:08:25",
            "2022-10-11T20:11:29",
            "2023-03-08T15:26:58",
            "2023-05-30T17:05:25"
        ],
        "createdDate": [
            "2022-09-20T22:08:08",
            "2022-09-20T22:08:25",
            "2022-10-11T20:11:29",
            "2023-03-08T15:26:58",
            "2023-05-30T17:05:25"
        ],
        "roleTypeIdList": [
            ["USER"],
            ["USER"],
            ["USER"],
            ["USER"],
            ["USER"]
        ],
        "glAccountList": [[], [], [], [], []],
        "contentList": [[], [], [], [], []],
        "userFieldDataList": [[], [], [], [], []],
        "connectionRelationUrlList": [[], [], [], [], []],
        "productStoreUrlList": [None, None, None, [], []]
    }

    try:
        # Initialize components
        db_config = DatabaseConfig()
        data_processor = FinaleDataProcessor()
        migrator = VendorMigrator(db_config)

        # Connect to database
        if not migrator.connect():
            logging.error("Failed to connect to database. Exiting.")
            return False

        # Load and process Finale data
        # You can replace this with actual data loading:
        # finale_data = load_finale_data(file_path='finale_data.json')
        finale_data = sample_finale_data

        if not finale_data:
            logging.error("No Finale data to process. Exiting.")
            return False

        # Process the data
        processed_vendors = data_processor.process_finale_data(finale_data)

        if not processed_vendors:
            logging.error("No vendor records processed. Exiting.")
            return False

        logging.info(f"Processed {len(processed_vendors)} vendor records")

        # Migrate to database
        migration_stats = migrator.migrate_vendors(processed_vendors)

        # Log migration results
        logging.info("Migration completed!")
        logging.info(f"Total records: {migration_stats['total']}")
        logging.info(f"Successfully inserted: {migration_stats['inserted']}")
        logging.info(f"Skipped (existing): {migration_stats['skipped']}")
        logging.info(f"Failed: {migration_stats['failed']}")

        return True

    except Exception as e:
        logging.error(f"Unexpected error during migration: {e}")
        return False

    finally:
        # Clean up
        if migrator:
            migrator.disconnect()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)