#!/usr/bin/env python3
"""
Finale Supplier Data Migration Script

This script migrates supplier data from Finale API to the local database system.
It handles the conversion of Finale's party data structure to the vendor table format.

Author: Migration Script
Date: 2025-01-17
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os
import requests
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class FinaleAPIClient:
    """Finale API client for fetching supplier data"""

    def __init__(self):
        self.base_url = "https://app.finaleinventory.com"
        self.api_endpoint = "/mercaso/api/partygroup"

        # API credentials from environment variables
        self.api_key = os.getenv('FINALE_API_KEY')
        self.username = os.getenv('FINALE_USERNAME')
        self.password = os.getenv('FINALE_PASSWORD')
        self.tenant_id = os.getenv('FINALE_TENANT_ID')

        # Request timeout settings
        self.timeout = int(os.getenv('FINALE_TIMEOUT', '30'))

        # Session for connection reuse
        self.session = requests.Session()
        self._setup_session()

    def _setup_session(self):
        """Setup session with default headers and authentication"""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Finale-Migration-Script/1.0'
        }

        # Add API key if available
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'

        # Add tenant ID if available
        if self.tenant_id:
            headers['X-Tenant-ID'] = self.tenant_id

        self.session.headers.update(headers)

        # Setup basic auth if username/password provided
        if self.username and self.password:
            self.session.auth = (self.username, self.password)

    def fetch_supplier_data(self, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Fetch supplier data from Finale API

        Args:
            params: Optional query parameters for the API call

        Returns:
            Supplier data from Finale API or None if failed
        """
        url = f"{self.base_url}{self.api_endpoint}"

        try:
            logging.info(f"Fetching supplier data from Finale API: {url}")

            # Default parameters
            default_params = {
                'limit': 1000,  # Adjust as needed
                'offset': 0
            }

            if params:
                default_params.update(params)

            response = self.session.get(
                url,
                params=default_params,
                timeout=self.timeout
            )

            # Check response status
            response.raise_for_status()

            # Parse JSON response
            data = response.json()

            logging.info(f"Successfully fetched supplier data. Records: {len(data.get('partyId', []))}")
            return data

        except requests.exceptions.RequestException as e:
            logging.error(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logging.error(f"Response status: {e.response.status_code}")
                logging.error(f"Response content: {e.response.text}")
            return None
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error fetching supplier data: {e}")
            return None

    def fetch_all_suppliers(self, batch_size: int = 1000) -> Optional[Dict[str, Any]]:
        """
        Fetch all suppliers using pagination

        Args:
            batch_size: Number of records to fetch per request

        Returns:
            Combined supplier data from all pages
        """
        all_data = {
            'partyId': [],
            'partyUrl': [],
            'statusId': [],
            'lastUpdatedDate': [],
            'createdDate': [],
            'roleTypeIdList': [],
            'glAccountList': [],
            'contentList': [],
            'userFieldDataList': [],
            'connectionRelationUrlList': [],
            'productStoreUrlList': []
        }

        offset = 0
        total_fetched = 0

        while True:
            params = {
                'limit': batch_size,
                'offset': offset
            }

            batch_data = self.fetch_supplier_data(params)

            if not batch_data:
                logging.error(f"Failed to fetch batch at offset {offset}")
                break

            # Check if we got any data
            party_ids = batch_data.get('partyId', [])
            if not party_ids:
                logging.info("No more data to fetch")
                break

            # Merge batch data into all_data
            for key in all_data.keys():
                if key in batch_data:
                    all_data[key].extend(batch_data[key])

            batch_count = len(party_ids)
            total_fetched += batch_count
            logging.info(f"Fetched batch: {batch_count} records (total: {total_fetched})")

            # If batch is smaller than batch_size, we've reached the end
            if batch_count < batch_size:
                break

            offset += batch_size

        if total_fetched > 0:
            logging.info(f"Successfully fetched all supplier data. Total records: {total_fetched}")
            return all_data
        else:
            logging.error("No supplier data fetched")
            return None

    def close(self):
        """Close the session"""
        if self.session:
            self.session.close()


class DatabaseConfig:
    """Database configuration class"""

    def __init__(self):
        # Database connection parameters
        # You can modify these or use environment variables
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'your_database')
        self.username = os.getenv('DB_USER', 'your_username')
        self.password = os.getenv('DB_PASSWORD', 'your_password')

    def get_connection_string(self) -> str:
        """Get database connection string"""
        return f"host={self.host} port={self.port} dbname={self.database} user={self.username} password={self.password}"


class FinaleDataProcessor:
    """Process Finale API data for migration"""

    @staticmethod
    def convert_status(finale_status: str) -> str:
        """Convert Finale status to system status"""
        status_mapping = {
            'PARTY_ENABLED': 'ACTIVE',
            'PARTY_DISABLED': 'DRAFT'
        }
        return status_mapping.get(finale_status, 'UNKNOWN')

    @staticmethod
    def parse_datetime(date_string: str) -> Optional[datetime]:
        """Parse Finale datetime string to Python datetime"""
        try:
            # Finale format: "2022-09-20T22:08:08"
            return datetime.fromisoformat(date_string)
        except (ValueError, TypeError) as e:
            logging.warning(f"Failed to parse datetime '{date_string}': {e}")
            return None

    @staticmethod
    def generate_vendor_name(party_id: str) -> str:
        """Generate vendor name from party ID"""
        return f"Finale Vendor {party_id}"

    def process_finale_data(self, finale_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process Finale API data and convert to vendor records

        Args:
            finale_data: Raw data from Finale API

        Returns:
            List of processed vendor records
        """
        processed_vendors = []

        # Get the length of data arrays
        party_ids = finale_data.get('partyId', [])
        party_urls = finale_data.get('partyUrl', [])
        status_ids = finale_data.get('statusId', [])
        last_updated_dates = finale_data.get('lastUpdatedDate', [])
        created_dates = finale_data.get('createdDate', [])

        # Ensure all arrays have the same length
        data_length = len(party_ids)
        if not all(len(arr) == data_length for arr in [party_urls, status_ids, last_updated_dates, created_dates]):
            logging.error("Data arrays have inconsistent lengths")
            return []

        # Process each vendor record
        for i in range(data_length):
            try:
                vendor_record = {
                    'id': str(uuid.uuid4()),  # Generate new UUID
                    'finale_id': party_ids[i],
                    'vendor_name': self.generate_vendor_name(party_ids[i]),
                    'vendor_contact_name': None,  # Not available in Finale data
                    'vendor_contact_tel': None,   # Not available in Finale data
                    'vendor_company_name': self.generate_vendor_name(party_ids[i]),
                    'status': self.convert_status(status_ids[i]),
                    'created_at': self.parse_datetime(created_dates[i]),
                    'created_by': 'FINALE_MIGRATION',
                    'updated_at': self.parse_datetime(last_updated_dates[i]),
                    'updated_by': 'FINALE_MIGRATION',
                    'deleted_at': None,
                    'deleted_by': None,
                    'created_user_name': 'Finale Migration Script',
                    'updated_user_name': 'Finale Migration Script',
                    'deleted_user_name': None,
                    'external_picking': False,  # Default value
                    'shutdown_window_enabled': False,  # Default value
                    'shutdown_window_start': '05:00:00',  # Default value
                    'shutdown_window_end': '06:00:00',    # Default value
                    'shutdown_window_days': 'FRIDAY,SATURDAY',  # Default value
                    'default_terms': None,
                    'default_lead_days': None,
                    'notes': f'Migrated from Finale. Original Party URL: {party_urls[i] if i < len(party_urls) else "N/A"}'
                }

                processed_vendors.append(vendor_record)
                logging.info(f"Processed vendor record for party ID: {party_ids[i]}")

            except Exception as e:
                logging.error(f"Error processing vendor record at index {i}: {e}")
                continue

        return processed_vendors


class VendorMigrator:
    """Handle vendor data migration to database"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.connection = None

    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(
                self.db_config.get_connection_string(),
                cursor_factory=RealDictCursor
            )
            self.connection.autocommit = False
            logging.info("Database connection established successfully")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to database: {e}")
            return False

    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logging.info("Database connection closed")

    def get_existing_vendor(self, finale_id: str) -> Optional[Dict[str, Any]]:
        """Get existing vendor record by finale_id"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM vendor WHERE finale_id = %s AND deleted_at IS NULL",
                    (finale_id,)
                )
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error checking existing vendor {finale_id}: {e}")
            return None

    def insert_vendor(self, vendor_data: Dict[str, Any]) -> bool:
        """Insert vendor record into database"""
        try:
            insert_query = """
                INSERT INTO vendor (
                    id, vendor_name, vendor_contact_name, vendor_contact_tel,
                    vendor_company_name, status, created_at, created_by,
                    updated_at, updated_by, deleted_at, deleted_by,
                    created_user_name, updated_user_name, deleted_user_name,
                    external_picking, finale_id, shutdown_window_enabled,
                    shutdown_window_start, shutdown_window_end, shutdown_window_days,
                    default_terms, default_lead_days, notes
                ) VALUES (
                    %(id)s, %(vendor_name)s, %(vendor_contact_name)s, %(vendor_contact_tel)s,
                    %(vendor_company_name)s, %(status)s, %(created_at)s, %(created_by)s,
                    %(updated_at)s, %(updated_by)s, %(deleted_at)s, %(deleted_by)s,
                    %(created_user_name)s, %(updated_user_name)s, %(deleted_user_name)s,
                    %(external_picking)s, %(finale_id)s, %(shutdown_window_enabled)s,
                    %(shutdown_window_start)s, %(shutdown_window_end)s, %(shutdown_window_days)s,
                    %(default_terms)s, %(default_lead_days)s, %(notes)s
                )
            """

            with self.connection.cursor() as cursor:
                cursor.execute(insert_query, vendor_data)
                logging.info(f"Inserted vendor: {vendor_data['finale_id']}")
                return True

        except Exception as e:
            logging.error(f"Error inserting vendor {vendor_data.get('finale_id', 'Unknown')}: {e}")
            return False

    def update_vendor(self, vendor_data: Dict[str, Any]) -> bool:
        """Update existing vendor record in database"""
        try:
            update_query = """
                UPDATE vendor SET
                    vendor_name = %(vendor_name)s,
                    vendor_contact_name = %(vendor_contact_name)s,
                    vendor_contact_tel = %(vendor_contact_tel)s,
                    vendor_company_name = %(vendor_company_name)s,
                    status = %(status)s,
                    updated_at = %(updated_at)s,
                    updated_by = %(updated_by)s,
                    updated_user_name = %(updated_user_name)s,
                    external_picking = %(external_picking)s,
                    shutdown_window_enabled = %(shutdown_window_enabled)s,
                    shutdown_window_start = %(shutdown_window_start)s,
                    shutdown_window_end = %(shutdown_window_end)s,
                    shutdown_window_days = %(shutdown_window_days)s,
                    default_terms = %(default_terms)s,
                    default_lead_days = %(default_lead_days)s,
                    notes = %(notes)s
                WHERE finale_id = %(finale_id)s AND deleted_at IS NULL
            """

            with self.connection.cursor() as cursor:
                cursor.execute(update_query, vendor_data)
                if cursor.rowcount > 0:
                    logging.info(f"Updated vendor: {vendor_data['finale_id']}")
                    return True
                else:
                    logging.warning(f"No rows updated for vendor: {vendor_data['finale_id']}")
                    return False

        except Exception as e:
            logging.error(f"Error updating vendor {vendor_data.get('finale_id', 'Unknown')}: {e}")
            return False

    def migrate_vendors(self, vendor_records: List[Dict[str, Any]], update_existing: bool = True) -> Dict[str, int]:
        """
        Migrate vendor records to database

        Args:
            vendor_records: List of processed vendor records
            update_existing: Whether to update existing vendors (True) or skip them (False)

        Returns:
            Dictionary with migration statistics
        """
        stats = {
            'total': len(vendor_records),
            'inserted': 0,
            'updated': 0,
            'skipped': 0,
            'failed': 0
        }

        try:
            for vendor_record in vendor_records:
                finale_id = vendor_record.get('finale_id')

                # Check if vendor already exists
                existing_vendor = self.get_existing_vendor(finale_id)

                if existing_vendor:
                    if update_existing:
                        # Update existing vendor
                        if self.update_vendor(vendor_record):
                            stats['updated'] += 1
                        else:
                            stats['failed'] += 1
                    else:
                        logging.info(f"Vendor {finale_id} already exists, skipping")
                        stats['skipped'] += 1
                else:
                    # Vendor doesn't exist, skip it (as per requirement)
                    logging.info(f"Vendor {finale_id} not found in database, skipping")
                    stats['skipped'] += 1

            # Commit transaction
            self.connection.commit()
            logging.info("Migration transaction committed successfully")

        except Exception as e:
            logging.error(f"Error during migration: {e}")
            self.connection.rollback()
            logging.info("Migration transaction rolled back")

        return stats


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('finale_migration.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_finale_data(file_path: str = None, json_data: str = None) -> Optional[Dict[str, Any]]:
    """
    Load Finale data from file or JSON string

    Args:
        file_path: Path to JSON file containing Finale data
        json_data: JSON string containing Finale data

    Returns:
        Parsed Finale data or None if failed
    """
    try:
        if file_path:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif json_data:
            return json.loads(json_data)
        else:
            logging.error("Either file_path or json_data must be provided")
            return None
    except Exception as e:
        logging.error(f"Error loading Finale data: {e}")
        return None


def main():
    """Main migration function"""
    setup_logging()
    logging.info("Starting Finale supplier data migration")

    api_client = None
    migrator = None

    try:
        # Initialize components
        api_client = FinaleAPIClient()
        db_config = DatabaseConfig()
        data_processor = FinaleDataProcessor()
        migrator = VendorMigrator(db_config)

        # Connect to database
        if not migrator.connect():
            logging.error("Failed to connect to database. Exiting.")
            return False

        # Fetch data from Finale API
        logging.info("Fetching supplier data from Finale API...")
        finale_data = api_client.fetch_all_suppliers()

        if not finale_data:
            logging.error("Failed to fetch data from Finale API. Exiting.")
            return False

        logging.info(f"Successfully fetched {len(finale_data.get('partyId', []))} supplier records from API")

        # Process the data
        processed_vendors = data_processor.process_finale_data(finale_data)

        if not processed_vendors:
            logging.error("No vendor records processed. Exiting.")
            return False

        logging.info(f"Processed {len(processed_vendors)} vendor records")

        # Migrate to database
        migration_stats = migrator.migrate_vendors(processed_vendors)

        # Log migration results
        logging.info("Migration completed!")
        logging.info(f"Total records: {migration_stats['total']}")
        logging.info(f"Successfully updated: {migration_stats['updated']}")
        logging.info(f"Skipped (not found): {migration_stats['skipped']}")
        logging.info(f"Failed: {migration_stats['failed']}")

        return True

    except Exception as e:
        logging.error(f"Unexpected error during migration: {e}")
        return False

    finally:
        # Clean up
        if api_client:
            api_client.close()
        if migrator:
            migrator.disconnect()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)