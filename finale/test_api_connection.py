#!/usr/bin/env python3
"""
Test script for Finale API connection

This script tests the connection to Finale API and displays sample data.
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from MigrateFinaleSupplierData import FinaleAPIClient, setup_logging


def test_api_connection():
    """Test the Finale API connection"""
    print("🔗 Testing Finale API Connection")
    print("=" * 50)
    
    setup_logging()
    
    # Check environment variables
    api_key = os.getenv('FINALE_API_KEY')
    username = os.getenv('FINALE_USERNAME')
    password = os.getenv('FINALE_PASSWORD')
    tenant_id = os.getenv('FINALE_TENANT_ID')
    
    print("📋 Configuration Check:")
    print(f"   API Key: {'✅ Set' if api_key else '❌ Not set'}")
    print(f"   Username: {'✅ Set' if username else '❌ Not set'}")
    print(f"   Password: {'✅ Set' if password else '❌ Not set'}")
    print(f"   Tenant ID: {'✅ Set' if tenant_id else '❌ Not set'}")
    
    if not api_key and not (username and password):
        print("\n❌ Error: No authentication method configured!")
        print("Please set either:")
        print("1. FINALE_API_KEY for API key authentication, or")
        print("2. FINALE_USERNAME and FINALE_PASSWORD for basic authentication")
        return False
    
    try:
        # Initialize API client
        api_client = FinaleAPIClient()
        
        print(f"\n🌐 API Endpoint: {api_client.base_url}{api_client.api_endpoint}")
        
        # Test with small batch first
        print("\n🧪 Testing API connection with small batch...")
        test_data = api_client.fetch_supplier_data(params={'limit': 5, 'offset': 0})
        
        if test_data:
            print("✅ API connection successful!")
            
            # Display sample data structure
            party_ids = test_data.get('partyId', [])
            print(f"\n📊 Sample Data Retrieved:")
            print(f"   Records found: {len(party_ids)}")
            
            if party_ids:
                print(f"   Sample Party IDs: {party_ids[:3]}...")
                
                # Show data structure
                print(f"\n📋 Data Structure:")
                for key, value in test_data.items():
                    if isinstance(value, list):
                        print(f"   {key}: {len(value)} items")
                    else:
                        print(f"   {key}: {type(value).__name__}")
                
                # Show first record details
                if len(party_ids) > 0:
                    print(f"\n🔍 First Record Details:")
                    print(f"   Party ID: {party_ids[0]}")
                    
                    status_ids = test_data.get('statusId', [])
                    if status_ids:
                        print(f"   Status: {status_ids[0]}")
                    
                    created_dates = test_data.get('createdDate', [])
                    if created_dates:
                        print(f"   Created: {created_dates[0]}")
                    
                    updated_dates = test_data.get('lastUpdatedDate', [])
                    if updated_dates:
                        print(f"   Updated: {updated_dates[0]}")
            
            return True
        else:
            print("❌ API connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API connection: {e}")
        return False
    
    finally:
        if 'api_client' in locals():
            api_client.close()


def test_full_fetch():
    """Test fetching all supplier data"""
    print("\n" + "=" * 50)
    print("🔄 Testing Full Data Fetch")
    print("=" * 50)
    
    try:
        api_client = FinaleAPIClient()
        
        print("⚠️  This will fetch ALL supplier data from Finale API")
        response = input("Continue? (y/N): ").strip().lower()
        
        if response != 'y':
            print("Skipped full fetch test")
            return True
        
        print("\n📥 Fetching all supplier data...")
        all_data = api_client.fetch_all_suppliers()
        
        if all_data:
            total_records = len(all_data.get('partyId', []))
            print(f"✅ Successfully fetched {total_records} total records")
            
            # Save sample to file for inspection
            sample_file = 'api_test_sample.json'
            sample_data = {}
            for key, value in all_data.items():
                if isinstance(value, list):
                    sample_data[key] = value[:5]  # First 5 records only
                else:
                    sample_data[key] = value
            
            with open(sample_file, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, default=str)
            
            print(f"📄 Sample data saved to: {sample_file}")
            return True
        else:
            print("❌ Failed to fetch all supplier data")
            return False
            
    except Exception as e:
        print(f"❌ Error during full fetch test: {e}")
        return False
    
    finally:
        if 'api_client' in locals():
            api_client.close()


def main():
    """Main test function"""
    print("🧪 Finale API Connection Test")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  No .env file found!")
        print("Please copy .env.example to .env and configure your API credentials")
        return False
    
    # Test basic connection
    if not test_api_connection():
        return False
    
    # Test full fetch (optional)
    test_full_fetch()
    
    print("\n🎉 API testing completed!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
