#!/usr/bin/env python3
"""
Test script for Finale Supplier Data Migration

This script tests the data processing logic without connecting to the database.
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from MigrateFinaleSupplierData import FinaleDataProcessor, load_finale_data


def test_data_processing():
    """Test the data processing functionality"""
    print("Testing Finale data processing...")
    
    # Load sample data
    finale_data = load_finale_data(file_path='sample_finale_data.json')
    
    if not finale_data:
        print("❌ Failed to load sample data")
        return False
    
    print("✅ Sample data loaded successfully")
    print(f"   - Found {len(finale_data.get('partyId', []))} party records")
    
    # Process the data
    processor = FinaleDataProcessor()
    processed_vendors = processor.process_finale_data(finale_data)
    
    if not processed_vendors:
        print("❌ Failed to process vendor data")
        return False
    
    print(f"✅ Processed {len(processed_vendors)} vendor records")
    
    # Display first processed record
    if processed_vendors:
        first_vendor = processed_vendors[0]
        print("\n📋 Sample processed vendor record:")
        print(f"   - ID: {first_vendor['id']}")
        print(f"   - Finale ID: {first_vendor['finale_id']}")
        print(f"   - Vendor Name: {first_vendor['vendor_name']}")
        print(f"   - Status: {first_vendor['status']}")
        print(f"   - Created At: {first_vendor['created_at']}")
        print(f"   - Updated At: {first_vendor['updated_at']}")
        print(f"   - Notes: {first_vendor['notes'][:50]}...")
    
    return True


def test_status_conversion():
    """Test status conversion logic"""
    print("\nTesting status conversion...")
    
    processor = FinaleDataProcessor()
    
    test_cases = [
        ('PARTY_ENABLED', 'ACTIVE'),
        ('PARTY_DISABLED', 'INACTIVE'),
        ('PARTY_SUSPENDED', 'SUSPENDED'),
        ('UNKNOWN_STATUS', 'UNKNOWN')
    ]
    
    all_passed = True
    for input_status, expected_output in test_cases:
        result = processor.convert_status(input_status)
        if result == expected_output:
            print(f"   ✅ {input_status} -> {result}")
        else:
            print(f"   ❌ {input_status} -> {result} (expected {expected_output})")
            all_passed = False
    
    return all_passed


def test_datetime_parsing():
    """Test datetime parsing logic"""
    print("\nTesting datetime parsing...")
    
    processor = FinaleDataProcessor()
    
    test_cases = [
        '2022-09-20T22:08:08',
        '2023-03-08T15:26:58',
        'invalid-date',
        None
    ]
    
    for date_string in test_cases:
        result = processor.parse_datetime(date_string)
        if date_string and date_string != 'invalid-date':
            if result:
                print(f"   ✅ {date_string} -> {result}")
            else:
                print(f"   ❌ {date_string} -> Failed to parse")
        else:
            print(f"   ✅ {date_string} -> {result} (expected None/failure)")
    
    return True


def main():
    """Run all tests"""
    print("🧪 Starting Finale Migration Tests\n")
    
    tests = [
        ("Data Processing", test_data_processing),
        ("Status Conversion", test_status_conversion),
        ("Datetime Parsing", test_datetime_parsing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    print('='*50)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
