#!/usr/bin/env python3
"""
Test script for related data processing (addresses, emails, phones)

This script tests the processing of address, email, and phone data from Finale API.
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from MigrateFinaleSupplierData import FinaleDataProcessor


def test_related_data_processing():
    """Test processing of addresses, emails, and phone numbers"""
    print("🧪 Testing Related Data Processing")
    print("=" * 50)
    
    # Sample Finale data with content information
    sample_data_with_content = {
        "partyId": ["100001", "100002"],
        "partyUrl": [
            "/mercaso/api/partygroup/100001",
            "/mercaso/api/partygroup/100002"
        ],
        "statusId": ["PARTY_ENABLED", "PARTY_ENABLED"],
        "lastUpdatedDate": [
            "2024-01-17T10:30:00",
            "2024-01-17T11:15:00"
        ],
        "createdDate": [
            "2024-01-17T10:30:00",
            "2024-01-17T11:15:00"
        ],
        "roleTypeIdList": [["USER"], ["USER"]],
        "glAccountList": [[], []],
        "contactMechList": [
            [
                {
                    "contactMechId": "100001",
                    "contactMechTypeId": "POSTAL_ADDRESS",
                    "contactMechPurposeTypeId": "GENERAL_LOCATION",
                    "address1": "123 Main Street",
                    "city": "New York",
                    "stateProvinceGeoId": "NY",
                    "postalCode": "10001",
                    "statusId": null
                },
                {
                    "contactMechId": "100002",
                    "contactMechTypeId": "EMAIL_ADDRESS",
                    "contactMechPurposeTypeId": "WORK_EMAIL",
                    "infoString": "<EMAIL>",
                    "statusId": null
                },
                {
                    "contactMechId": "100003",
                    "contactMechTypeId": "TELECOM_NUMBER",
                    "contactMechPurposeTypeId": "PHONE_WORK",
                    "infoString": "************",
                    "statusId": null
                }
            ],
            [
                {
                    "contactMechId": "100004",
                    "contactMechTypeId": "POSTAL_ADDRESS",
                    "contactMechPurposeTypeId": "WAREHOUSE_LOCATION",
                    "address1": "456 Oak Avenue",
                    "city": "Los Angeles",
                    "stateProvinceGeoId": "CA",
                    "postalCode": "90210",
                    "statusId": null
                },
                {
                    "contactMechId": "100005",
                    "contactMechTypeId": "EMAIL_ADDRESS",
                    "contactMechPurposeTypeId": "WORK_EMAIL",
                    "infoString": "<EMAIL>",
                    "statusId": null
                }
            ]
        ],
        "userFieldDataList": [[], []],
        "connectionRelationUrlList": [[], []],
        "productStoreUrlList": [[], []]
    }
    
    try:
        # Process the data
        processor = FinaleDataProcessor()
        processed_vendors = processor.process_finale_data(sample_data_with_content)
        
        if not processed_vendors:
            print("❌ Failed to process vendor data")
            return False
        
        print(f"✅ Processed {len(processed_vendors)} vendor records")
        
        # Display detailed results
        for i, vendor in enumerate(processed_vendors):
            print(f"\n📋 Vendor {i+1} Details:")
            print(f"   Finale ID: {vendor['finale_id']}")
            print(f"   Vendor Name: {vendor['vendor_name']}")
            print(f"   Contact Name: {vendor['vendor_contact_name']}")
            print(f"   Contact Tel: {vendor['vendor_contact_tel']}")
            
            # Display addresses
            addresses = vendor.get('addresses', [])
            print(f"   Addresses: {len(addresses)}")
            for j, addr in enumerate(addresses):
                print(f"     {j+1}. {addr['street_address']}, {addr['city']}, {addr['state']} {addr['postal_code']}")
                print(f"        Purpose: {addr['purpose']}")
            
            # Display emails
            emails = vendor.get('emails', [])
            print(f"   Emails: {len(emails)}")
            for j, email in enumerate(emails):
                print(f"     {j+1}. {email['email']} ({email['email_type']})")
            
            # Display phones
            phones = vendor.get('phones', [])
            print(f"   Phones: {len(phones)}")
            for j, phone in enumerate(phones):
                print(f"     {j+1}. {phone['phone_number']} ({phone['phone_type']})")
        
        # Summary statistics
        total_addresses = sum(len(v.get('addresses', [])) for v in processed_vendors)
        total_emails = sum(len(v.get('emails', [])) for v in processed_vendors)
        total_phones = sum(len(v.get('phones', [])) for v in processed_vendors)
        
        print(f"\n📊 Summary Statistics:")
        print(f"   Total Addresses: {total_addresses}")
        print(f"   Total Emails: {total_emails}")
        print(f"   Total Phones: {total_phones}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False


def test_data_validation():
    """Test data validation for addresses, emails, and phones"""
    print("\n" + "=" * 50)
    print("🔍 Testing Data Validation")
    print("=" * 50)
    
    processor = FinaleDataProcessor()
    
    # Test address validation
    print("\n📍 Address Validation Tests:")
    address_tests = [
        {"address1": "123 Main St", "city": "New York", "expected": True},
        {"address1": "123", "city": "NY", "expected": False},  # Too short
        {"address1": "", "city": "New York", "expected": False},  # Missing street
        {"address1": "123 Main St", "city": "", "expected": False},  # Missing city
    ]

    for test in address_tests:
        result = processor.extract_address_from_contact_mech("test-id", test, None)
        success = (result is not None) == test["expected"]
        status = "✅" if success else "❌"
        print(f"   {status} Street: '{test['address1']}', City: '{test['city']}' -> {'Valid' if result else 'Invalid'}")

    # Test email validation
    print("\n📧 Email Validation Tests:")
    email_tests = [
        {"infoString": "<EMAIL>", "expected": True},
        {"infoString": "invalid-email", "expected": False},
        {"infoString": "test@", "expected": False},
        {"infoString": "@example.com", "expected": False},
        {"infoString": "", "expected": False},
    ]

    for test in email_tests:
        result = processor.extract_email_from_contact_mech("test-id", test, None)
        success = (result is not None) == test["expected"]
        status = "✅" if success else "❌"
        print(f"   {status} Email: '{test['infoString']}' -> {'Valid' if result else 'Invalid'}")

    # Test phone validation
    print("\n📞 Phone Validation Tests:")
    phone_tests = [
        {"infoString": "+1-************", "expected": True},
        {"infoString": "************", "expected": True},
        {"infoString": "5551234567", "expected": True},
        {"infoString": "123", "expected": False},  # Too short
        {"infoString": "", "expected": False},
        {"infoString": "abc-def-ghij", "expected": False},  # No digits
    ]

    for test in phone_tests:
        result = processor.extract_phone_from_contact_mech("test-id", test, None)
        success = (result is not None) == test["expected"]
        status = "✅" if success else "❌"
        print(f"   {status} Phone: '{test['infoString']}' -> {'Valid' if result else 'Invalid'}")
    
    return True


def main():
    """Main test function"""
    print("🧪 Related Data Processing Tests")
    print("=" * 50)
    
    tests = [
        ("Related Data Processing", test_related_data_processing),
        ("Data Validation", test_data_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            print(f"\n❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    print('='*50)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
