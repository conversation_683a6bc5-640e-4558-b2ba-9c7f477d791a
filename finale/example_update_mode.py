#!/usr/bin/env python3
"""
Example script demonstrating the update-only mode for Finale supplier migration

This script shows how to use the migration tool to update existing vendor records
without creating new ones.
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from MigrateFinaleSupplierData import (
    DatabaseConfig, 
    FinaleDataProcessor, 
    VendorMigrator,
    load_finale_data,
    setup_logging
)


def demonstrate_update_mode():
    """Demonstrate the update-only migration mode"""
    
    setup_logging()
    logging.info("Starting Finale supplier update demonstration")
    
    # Sample data with some existing and some non-existing finale_ids
    sample_data = {
        "partyId": [
            "100001",  # Assume this exists in database
            "100002",  # Assume this exists in database  
            "999999",  # Assume this doesn't exist
            "888888"   # Assume this doesn't exist
        ],
        "partyUrl": [
            "/mercaso/api/partygroup/100001",
            "/mercaso/api/partygroup/100002",
            "/mercaso/api/partygroup/999999",
            "/mercaso/api/partygroup/888888"
        ],
        "statusId": [
            "PARTY_ENABLED",
            "PARTY_DISABLED",
            "PARTY_ENABLED",
            "PARTY_SUSPENDED"
        ],
        "lastUpdatedDate": [
            "2024-01-17T10:30:00",
            "2024-01-17T11:15:00",
            "2024-01-17T12:00:00",
            "2024-01-17T13:45:00"
        ],
        "createdDate": [
            "2022-09-20T22:08:08",
            "2022-09-20T22:08:25",
            "2023-01-01T00:00:00",
            "2023-06-15T14:30:00"
        ],
        "roleTypeIdList": [["USER"], ["USER"], ["USER"], ["USER"]],
        "glAccountList": [[], [], [], []],
        "contentList": [[], [], [], []],
        "userFieldDataList": [[], [], [], []],
        "connectionRelationUrlList": [[], [], [], []],
        "productStoreUrlList": [[], [], [], []]
    }
    
    try:
        # Initialize components
        db_config = DatabaseConfig()
        data_processor = FinaleDataProcessor()
        migrator = VendorMigrator(db_config)
        
        # Connect to database
        if not migrator.connect():
            logging.error("Failed to connect to database. Please check your configuration.")
            print("\n❌ Database connection failed!")
            print("Please ensure:")
            print("1. Database is running")
            print("2. .env file is configured correctly")
            print("3. Database credentials are valid")
            return False
        
        print("✅ Connected to database successfully")
        
        # Process the data
        processed_vendors = data_processor.process_finale_data(sample_data)
        
        if not processed_vendors:
            logging.error("No vendor records processed")
            return False
        
        print(f"📊 Processed {len(processed_vendors)} vendor records")
        
        # Show what will happen
        print("\n🔍 Checking existing records...")
        for vendor in processed_vendors:
            finale_id = vendor['finale_id']
            existing = migrator.get_existing_vendor(finale_id)
            if existing:
                print(f"   ✏️  Will UPDATE: {finale_id} (found in database)")
            else:
                print(f"   ⏭️  Will SKIP: {finale_id} (not found in database)")
        
        # Perform migration
        print("\n🚀 Starting migration...")
        migration_stats = migrator.migrate_vendors(processed_vendors, update_existing=True)
        
        # Display results
        print("\n📈 Migration Results:")
        print(f"   Total records processed: {migration_stats['total']}")
        print(f"   Successfully updated: {migration_stats['updated']}")
        print(f"   Skipped (not found): {migration_stats['skipped']}")
        print(f"   Failed: {migration_stats['failed']}")
        
        if migration_stats['updated'] > 0:
            print(f"\n✅ Successfully updated {migration_stats['updated']} vendor records!")
        
        if migration_stats['skipped'] > 0:
            print(f"\n⏭️ Skipped {migration_stats['skipped']} records (not found in database)")
        
        if migration_stats['failed'] > 0:
            print(f"\n❌ Failed to process {migration_stats['failed']} records")
        
        return migration_stats['failed'] == 0
        
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        print(f"\n❌ Error: {e}")
        return False
    
    finally:
        if migrator:
            migrator.disconnect()


def show_configuration_help():
    """Show configuration help"""
    print("📋 Configuration Help:")
    print("1. Copy .env.example to .env")
    print("2. Update database credentials in .env file:")
    print("   DB_HOST=your_database_host")
    print("   DB_PORT=5432")
    print("   DB_NAME=your_database_name")
    print("   DB_USER=your_username")
    print("   DB_PASSWORD=your_password")
    print("\n3. Ensure your database contains some vendor records with finale_id values")
    print("4. Run this script to see the update-only behavior")


def main():
    """Main function"""
    print("🔄 Finale Supplier Update Mode Demonstration")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  No .env file found!")
        show_configuration_help()
        return False
    
    # Run demonstration
    success = demonstrate_update_mode()
    
    if success:
        print("\n🎉 Demonstration completed successfully!")
    else:
        print("\n💡 Tips for troubleshooting:")
        print("1. Check database connection settings in .env")
        print("2. Ensure vendor table exists with some test data")
        print("3. Check the log file: finale_migration.log")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
