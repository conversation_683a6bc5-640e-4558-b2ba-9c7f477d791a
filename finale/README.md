# Finale Supplier Data Migration

这个脚本用于将Finale系统的供应商数据迁移到本地数据库系统中。

## 功能特性

- **自动从Finale API获取数据**：调用 `https://app.finaleinventory.com/mercaso/api/partygroup`
- 将Finale API返回的party数据转换为vendor表格式
- **仅更新已存在的vendor记录**（根据finale_id查询）
- 不存在的记录将被跳过（不会新增）
- **一次性获取所有数据**：无需分页，直接获取全部supplier数据
- 支持多种认证方式（API Key 或 用户名/密码）
- 支持数据验证和重复检查
- 提供详细的日志记录
- 支持事务处理确保数据一致性
- 可配置的数据库连接参数

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置数据库和API连接信息：

### 数据库配置
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password
```

### Finale API配置
选择以下认证方式之一：

**方式1：API Key认证（推荐）**
```env
FINALE_API_KEY=your_api_key_here
```

**方式2：用户名/密码认证**
```env
FINALE_USERNAME=your_finale_username
FINALE_PASSWORD=your_finale_password
```

**可选配置**
```env
FINALE_TENANT_ID=your_tenant_id  # 如果需要
FINALE_TIMEOUT=30               # API超时时间（秒）
```

## 使用方法

### 步骤1：测试API连接
```bash
python test_api_connection.py
```
这将测试Finale API连接并显示示例数据。

### 步骤2：运行迁移脚本
```bash
python MigrateFinaleSupplierData.py
```
脚本将：
1. 自动从Finale API获取所有supplier数据
2. 处理和转换数据格式
3. 更新数据库中已存在的vendor记录

### 可选：从本地文件加载数据
如果你有本地的JSON数据文件，可以修改脚本使用本地数据：
```python
# 在main()函数中替换API调用
finale_data = load_finale_data(file_path='your_finale_data.json')
```

## 数据映射

### Vendor表映射
| Finale字段 | 目标表字段 | 说明 |
|-----------|-----------|------|
| partyId | finale_id | Finale系统的原始ID |
| statusId | status | 状态转换（PARTY_ENABLED -> ACTIVE） |
| createdDate | created_at | 创建时间 |
| lastUpdatedDate | updated_at | 更新时间 |
| contentList[contact] | vendor_contact_name | 从联系人信息提取 |
| contentList[contact] | vendor_contact_tel | 从联系人信息提取 |
| - | vendor_name | 基于partyId生成 |
| - | vendor_company_name | 基于partyId生成 |

### 相关表映射
| 源数据 | 目标表 | 说明 |
|-------|--------|------|
| contactMechList[POSTAL_ADDRESS] | address | 地址信息（entity_type='VENDOR'） |
| contactMechList[EMAIL_ADDRESS] | email | 邮箱信息（entity_type='VENDOR'） |
| contactMechList[TELECOM_NUMBER] | phone_number | 电话信息（entity_type='VENDOR'） |

### ContactMech字段映射详情

**地址映射 (POSTAL_ADDRESS)**
| Finale字段 | 目标字段 | 说明 |
|-----------|---------|------|
| address1 | street_address | 街道地址 |
| city | city | 城市 |
| stateProvinceGeoId | state | 州/省 |
| postalCode | postal_code | 邮政编码 |
| address2 | additional_lines | 附加地址信息 |
| contactMechPurposeTypeId | purpose | 地址用途映射 |

**邮箱映射 (EMAIL_ADDRESS)**
| Finale字段 | 目标字段 | 说明 |
|-----------|---------|------|
| infoString | email | 邮箱地址 |
| contactMechPurposeTypeId | email_type | 邮箱类型映射 |

**电话映射 (TELECOM_NUMBER)**
| Finale字段 | 目标字段 | 说明 |
|-----------|---------|------|
| infoString | phone_number | 电话号码 |
| contactMechPurposeTypeId | phone_type | 电话类型映射 |

## 日志文件

脚本运行时会生成 `finale_migration.log` 文件，包含详细的迁移过程记录。

## 工作模式

**重要说明**：此脚本采用"仅更新现有记录"模式：

1. 🔍 **查询阶段**：根据finale_id查询vendor表中是否存在对应记录
2. ✏️ **更新阶段**：如果记录存在，则更新该记录的相关字段
3. ⏭️ **跳过阶段**：如果记录不存在，则跳过该条数据（不会新增记录）

## 注意事项

1. **仅更新已存在的供应商**（基于finale_id查询）
2. **不会新增任何新的供应商记录**
3. **自动处理相关数据**：地址、邮箱、电话信息会自动从contentList中提取并更新
4. **数据验证**：地址、邮箱、电话数据都有基本的格式验证
5. **数据清理**：更新vendor时会先删除旧的相关数据，再插入新数据
6. 所有数据库操作都在事务中执行，出错时会自动回滚

## 错误处理

- 数据库连接失败：检查数据库配置和网络连接
- 数据格式错误：检查Finale API返回的数据格式
- 重复数据：脚本会自动跳过已存在的记录

## 扩展功能

如果后续需要处理地址、邮箱、电话等信息，可以在 `VendorMigrator` 类中添加相应的方法：

```python
def insert_address(self, vendor_id: str, address_data: Dict[str, Any]) -> bool:
    # 实现地址插入逻辑
    pass

def insert_email(self, vendor_id: str, email_data: Dict[str, Any]) -> bool:
    # 实现邮箱插入逻辑
    pass

def insert_phone(self, vendor_id: str, phone_data: Dict[str, Any]) -> bool:
    # 实现电话插入逻辑
    pass
```
