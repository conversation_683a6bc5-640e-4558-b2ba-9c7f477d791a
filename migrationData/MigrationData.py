import pandas as pd
import json
import sqlalchemy
from urllib.parse import quote_plus
from sqlalchemy import create_engine, text

# 源数据库连接 (本地)
target_engine = create_engine('postgresql+psycopg2://item_management_user:mercaso@localhost:5432/item_management_service')

# 目标数据库连接 (远程)

#dev
#username = quote_plus("v-github-L-item-man-nWk71ziZQcwymzPuyV9g-1736492785")
#password = quote_plus("eIJG-XS1zBqZ2J94aaeR")
#host = "postgres-dev-us-west-2-001.cfiuu22mcwgq.us-west-2.rds.amazonaws.com"
#port = "5432"
#database = "item_management_service"
#connection_string = f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"

#sat
#username = quote_plus("v-github-B-item-man-4dN9n3Q0Adv34fP60Gol-1726130148")
#password = quote_plus("Yj8i4oKH-I6XwhwkIPNr")
#host = "postgres-sat-us-west-2-001.cheeuoig0z9s.us-west-2.rds.amazonaws.com"
#port = "5432"
#database = "item_management_service"
#connection_string = f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"


#产线
username = quote_plus("v-aws-ops/-item-man-W1IsjXPuKRD01c6Blrcf-1736238838")
password = quote_plus("O-r9qxJ4aljP66g62xoC")
host = "postgres-prod-us-west-2-001.cdw2ckiycf4f.us-west-2.rds.amazonaws.com"
port = "5432"
database = "item_management_service"
connection_string = f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"


source_engine = sqlalchemy.create_engine(connection_string)

# 获取所有表名
tables_to_migrate = ['vendor','vendor_item']

# 遍历每个表并迁移数据
for table in tables_to_migrate:
  print(f"Migrating table {table}...")

  # 清空目标表中的数据
  with target_engine.connect() as conn:
    print(f"Truncate {table}  successfully.")
    conn.execute(text(f'TRUNCATE TABLE {table} RESTART IDENTITY CASCADE;'))
    conn.commit()  # 确保提交事务

# 从源数据库读取表数据
  df = pd.read_sql(f'SELECT * FROM {table}', source_engine)

  # 将数据写入目标数据库
  print("正在将数据写入目标数据库")
  df.to_sql(table, target_engine, if_exists='append', index=False)

  print(f"Table {table} migrated successfully.")


  # 修改表字段类型并创建索引
with target_engine.connect() as conn:
  # 修改字段类型和创建索引的 SQL 语句
  queries = [
    # category 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE category
      ALTER COLUMN id TYPE uuid USING id::uuid,
      ALTER COLUMN name TYPE varchar(255),
      ALTER COLUMN icon TYPE varchar(255),
      ALTER COLUMN description TYPE varchar(255),
      ALTER COLUMN parent_category_id TYPE uuid USING parent_category_id::uuid,
      ALTER COLUMN status TYPE varchar(50),
      ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
      ALTER COLUMN created_by TYPE varchar(255),
      ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
      ALTER COLUMN updated_by TYPE varchar(255),
      ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
      ALTER COLUMN deleted_by TYPE varchar(255);

-- Step 2: 添加 primary key 约束
    ALTER TABLE category
      ADD PRIMARY KEY (id);

-- Step 3: 添加必要的索引
    CREATE INDEX category_parent_category_id_idx ON category (parent_category_id);
    CREATE INDEX category_name_idx ON category (name);
    
    -- Step 4: 修改表的所有权
/*    ALTER TABLE category
        OWNER TO item_management_user;*/
    """,
    # attribute 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE attribute
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN category_id TYPE uuid USING category_id::uuid,
        ALTER COLUMN name TYPE varchar(100),
        ALTER COLUMN description TYPE varchar(255),
        ALTER COLUMN attribute_format TYPE varchar(50),
        ALTER COLUMN status TYPE varchar(50),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE attribute
        ADD PRIMARY KEY (id);
    
    -- Step 3: 添加必要的索引
    CREATE INDEX attribute_category_id_idx ON attribute (category_id);
    
    -- Step 4: 修改表的所有权
    /*ALTER TABLE attribute
        OWNER TO item_management_user;*/
    """,
    # brand 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE brand
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN name TYPE varchar(255),
        ALTER COLUMN logo TYPE varchar(255),
        ALTER COLUMN description TYPE varchar(255),
        ALTER COLUMN status TYPE varchar(50),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE brand
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE brand
        OWNER TO item_management_user;*/
    """,
    # company 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE company
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN company_id TYPE bigint USING company_id::bigint,
        ALTER COLUMN name TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE company
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE company
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX company_company_id_idx ON company (company_id);
    """,

    # location 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE location
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN location_id TYPE bigint USING location_id::bigint,
        ALTER COLUMN company_uuid TYPE uuid USING company_uuid::uuid,
        ALTER COLUMN name TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE location
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE location
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX location_company_uuid_idx ON location (company_uuid);
    
    """,

    # item 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN category_id TYPE uuid USING category_id::uuid,
        ALTER COLUMN brand_id TYPE uuid USING brand_id::uuid,
        ALTER COLUMN name TYPE varchar(255),
        ALTER COLUMN title TYPE varchar(255),
        ALTER COLUMN sku_number TYPE varchar(255),
        ALTER COLUMN description TYPE text,
        ALTER COLUMN note TYPE text,
        ALTER COLUMN photo TYPE text,
        ALTER COLUMN primary_vendor_id TYPE uuid USING primary_vendor_id::uuid,
        ALTER COLUMN detail TYPE text,
        ALTER COLUMN package_type TYPE varchar(255),
        ALTER COLUMN package_size TYPE integer USING package_size::integer,
        ALTER COLUMN shelf_life TYPE varchar(255),
        ALTER COLUMN item_type TYPE varchar(255),
        ALTER COLUMN sales_status TYPE varchar(255),
        ALTER COLUMN availability_status TYPE varchar(255),
        ALTER COLUMN handle TYPE varchar(255),
        ALTER COLUMN company_id TYPE bigint USING company_id::bigint,
        ALTER COLUMN location_id TYPE bigint USING location_id::bigint,
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE item
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_category_id_idx ON item (category_id);
    CREATE INDEX item_brand_id_idx ON item (brand_id);
    CREATE UNIQUE INDEX item_sku_number_idx ON item (sku_number);
    CREATE INDEX item_updated_at_index ON item (updated_at DESC); 
    """,

    # item_attribute 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_attribute
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN attribute_id TYPE uuid USING attribute_id::uuid,
        ALTER COLUMN value TYPE text,
        ALTER COLUMN unit TYPE varchar(32),
        ALTER COLUMN attribute_type TYPE varchar(255),
        ALTER COLUMN sort TYPE integer USING sort::integer,
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_attribute
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE item_attribute
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_attribute_item_id_idx ON item_attribute (item_id);
    CREATE INDEX item_attribute_attribute_id_idx ON item_attribute (attribute_id);
    """,

    # item_image 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_image
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN file_name TYPE text,
        ALTER COLUMN image_type TYPE varchar(255),
        ALTER COLUMN status TYPE varchar(255),
        ALTER COLUMN sort TYPE integer USING sort::integer,
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_image
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
   /* ALTER TABLE item_image
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_image_item_id_idx ON item_image (item_id);
    """,


    # item_upc 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_upc
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN upc_number TYPE text,
        ALTER COLUMN upc_type TYPE varchar(64),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_upc
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE item_upc
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_upc_item_id_idx ON item_upc (item_id);

    """,

    # item_tag 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_tag
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN tag_name TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_tag
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE item_tag
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_tag_item_id_idx ON item_tag (item_id);
    """,

    # item_promo_price 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_promo_price
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN crv TYPE numeric USING crv::numeric,
        ALTER COLUMN promo_price TYPE numeric USING promo_price::numeric,
        ALTER COLUMN promo_price_individual TYPE numeric USING promo_price_individual::numeric,
        ALTER COLUMN promo_price_plus_crv TYPE numeric USING promo_price_plus_crv::numeric,
        ALTER COLUMN promo_begin_time TYPE timestamp USING promo_begin_time::timestamp,
        ALTER COLUMN promo_end_time TYPE timestamp USING promo_end_time::timestamp,
        ALTER COLUMN promo_flag TYPE boolean USING promo_flag::boolean,
        ALTER COLUMN promo_live_check TYPE varchar(255),
        ALTER COLUMN promo_pricing_validation TYPE text,
        ALTER COLUMN status TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_promo_price
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
   /* ALTER TABLE item_promo_price
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_promo_price_item_id_idx ON item_promo_price (item_id);
    """,
    
    
    # item_reg_price 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE item_reg_price
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN crv TYPE numeric USING crv::numeric,
        ALTER COLUMN reg_price TYPE numeric USING reg_price::numeric,
        ALTER COLUMN reg_price_individual TYPE numeric USING reg_price_individual::numeric,
        ALTER COLUMN reg_price_plus_crv TYPE numeric USING reg_price_plus_crv::numeric,
        ALTER COLUMN status TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE item_reg_price
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE item_reg_price
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX item_reg_price_item_id_idx ON item_reg_price (item_id);
    """,


    # vendor 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE vendor
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN vendor_name TYPE varchar(255),
        ALTER COLUMN vendor_contact_name TYPE varchar(255),
        ALTER COLUMN vendor_contact_tel TYPE varchar(255),
        ALTER COLUMN vendor_company_name TYPE varchar(255),
        ALTER COLUMN status TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE vendor
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE vendor
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX vendor_vendor_name_idx ON vendor (vendor_name);

    """,


    # vendor_item 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE vendor_item
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN vendor_id TYPE uuid USING vendor_id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN vendor_sku_number TYPE varchar(255),
        ALTER COLUMN vendor_item_name TYPE varchar(255),
        ALTER COLUMN note TYPE text,
        ALTER COLUMN status_change_reason TYPE text,
        ALTER COLUMN aisle TYPE varchar(255),
        ALTER COLUMN lowest_cost TYPE numeric USING lowest_cost::numeric,
        ALTER COLUMN highest_cost TYPE numeric USING highest_cost::numeric,
        ALTER COLUMN cost TYPE numeric USING cost::numeric,
        ALTER COLUMN secondary_cost TYPE numeric USING secondary_cost::numeric,
        ALTER COLUMN status TYPE varchar(255),
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE vendor_item
        ADD PRIMARY KEY (id);
    
    -- Step 3: 修改表的所有权
    /*ALTER TABLE vendor_item
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建必要的索引
    CREATE INDEX vendor_item_vendor_id_idx ON vendor_item (vendor_id);
    CREATE INDEX vendor_item_item_id_idx ON vendor_item (item_id);
    CREATE INDEX vendor_item_vendor_sku_number_idx ON vendor_item (vendor_sku_number);
    

    """,


    # plytix_other_data 表
    """
    -- Step 1: 修改列的数据类型
    ALTER TABLE plytix_other_data
        ALTER COLUMN id TYPE uuid USING id::uuid,
        ALTER COLUMN item_id TYPE uuid USING item_id::uuid,
        ALTER COLUMN sku TYPE varchar(255),
        ALTER COLUMN body_html TYPE text,
        ALTER COLUMN type TYPE varchar(255),
        ALTER COLUMN variant_cost TYPE numeric USING variant_cost::numeric,
        ALTER COLUMN variant_fulfillment_service TYPE varchar(255),
        ALTER COLUMN variant_inventory_policy TYPE varchar(255),
        ALTER COLUMN variant_inventory_tracker TYPE varchar(255),
        ALTER COLUMN variant_requires_shipping TYPE boolean USING variant_requires_shipping::boolean,
        ALTER COLUMN variant_taxable TYPE boolean USING variant_taxable::boolean,
        ALTER COLUMN published_scope TYPE varchar(255),
        ALTER COLUMN published_at TYPE varchar(64),
        ALTER COLUMN upc_reference TYPE varchar(255),
        ALTER COLUMN awesome_price TYPE numeric USING awesome_price::numeric,
        ALTER COLUMN banner TYPE varchar(255),
        ALTER COLUMN banner_expiry TYPE varchar(255),
        ALTER COLUMN banner_flag TYPE varchar(255),
        ALTER COLUMN clazz TYPE varchar(255),
        ALTER COLUMN cooler TYPE boolean USING cooler::boolean,
        ALTER COLUMN new_description TYPE varchar(255),
        ALTER COLUMN files_migration TYPE text,
        ALTER COLUMN id_tag TYPE varchar(255),
        ALTER COLUMN image_alt_text TYPE varchar(255),
        ALTER COLUMN image_height TYPE double precision USING image_height::double precision,
        ALTER COLUMN image_notes TYPE varchar(255),
        ALTER COLUMN image_width TYPE double precision USING image_width::double precision,
        ALTER COLUMN mfc_item TYPE boolean USING mfc_item::boolean,
        ALTER COLUMN prop_65 TYPE boolean USING prop_65::boolean,
        ALTER COLUMN secondary_handle TYPE varchar(255),
        ALTER COLUMN variant_inventory_item_id TYPE varchar(255),
        ALTER COLUMN variant_jc_show_app_price TYPE double precision USING variant_jc_show_app_price::double precision,
        ALTER COLUMN venture_partners_cost TYPE numeric USING venture_partners_cost::numeric,
        ALTER COLUMN product_id TYPE varchar(255),
        ALTER COLUMN variation_of TYPE varchar(255),
        ALTER COLUMN variants TYPE varchar(255),
        ALTER COLUMN assets TYPE text,
        ALTER COLUMN created_at TYPE timestamp USING created_at::timestamp,
        ALTER COLUMN created_by TYPE varchar(255),
        ALTER COLUMN updated_at TYPE timestamp USING updated_at::timestamp,
        ALTER COLUMN updated_by TYPE varchar(255),
        ALTER COLUMN deleted_at TYPE timestamp USING deleted_at::timestamp,
        ALTER COLUMN deleted_by TYPE varchar(255);
    
    -- Step 2: 添加 primary key 约束
    ALTER TABLE plytix_other_data
        ADD PRIMARY KEY (id);
        
    -- Step 3: 修改表的所有权
    /*ALTER TABLE plytix_other_data
        OWNER TO item_management_user;*/
    
    -- Step 4: 创建索引
    CREATE INDEX plytix_other_data_item_id_idx ON plytix_other_data (item_id);

    """
    # 其他表的修改和索引创建可以按需添加
  ]

  # 执行 SQL 语句
  # for query in queries:
    # conn.execute(text(query))
    # print("Executed query:\n", query)

print("Data migration completed.")
