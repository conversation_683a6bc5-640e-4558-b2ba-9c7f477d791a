"""
专业ETF投资分析提示词生成器
基于系统化的ETF投资决策框架
"""
from typing import Dict, List, Optional
from datetime import datetime
import pandas as pd

class ProfessionalPrompt:
    """专业ETF投资分析提示词生成器"""
    
    def __init__(self, investor_style: str = "稳健型"):
        """
        初始化提示词生成器
        
        Args:
            investor_style: 投资风格 - 稳健型/成长型/波段型/被动型
        """
        self.investor_style = investor_style
        self.decision_framework = self._get_decision_framework()
    
    def _get_decision_framework(self) -> Dict:
        """获取投资决策框架"""
        frameworks = {
            "稳健型": {
                "宏观经济形势": 30,
                "行业主题趋势": 20,
                "技术面分析": 10,
                "资金流向": 10,
                "基金特性": 15,
                "风险控制": 10,
                "情绪事件": 5
            },
            "成长型": {
                "宏观经济形势": 10,
                "行业主题趋势": 30,
                "技术面分析": 20,
                "资金流向": 10,
                "基金特性": 10,
                "风险控制": 5,
                "情绪事件": 15
            },
            "波段型": {
                "宏观经济形势": 5,
                "行业主题趋势": 10,
                "技术面分析": 40,
                "资金流向": 20,
                "基金特性": 5,
                "风险控制": 10,
                "情绪事件": 10
            },
            "被动型": {
                "宏观经济形势": 30,
                "行业主题趋势": 20,
                "技术面分析": 5,
                "资金流向": 5,
                "基金特性": 30,
                "风险控制": 10,
                "情绪事件": 0
            }
        }
        return frameworks.get(self.investor_style, frameworks["稳健型"])
    
    def generate_analysis_prompt(self, etf_data: List[Dict], portfolio_analysis: Optional[Dict] = None) -> str:
        """生成专业的ETF投资分析提示词"""
        
        # 构建系统角色定义
        system_role = self._build_system_role()
        
        # 构建投资组合概览
        portfolio_overview = self._build_portfolio_overview(portfolio_analysis or {})
        
        # 构建ETF详细分析
        etf_details = self._build_etf_details(etf_data)
        
        # 构建分析框架说明
        framework_guide = self._build_framework_guide()
        
        # 构建具体分析要求
        analysis_requirements = self._build_analysis_requirements()
        
        # 组合完整提示词
        prompt = f"""
{system_role}

{portfolio_overview}

{etf_details}

{framework_guide}

{analysis_requirements}

请基于以上信息，按照专业ETF投资决策框架进行深度分析，并提供具体的操作建议。
"""
        
        return prompt.strip()
    
    def _build_system_role(self) -> str:
        """构建系统角色定义"""
        return f"""
## 系统角色定义

你是一位专业的ETF投资顾问，具备以下专业能力：
- 深度的宏观经济分析能力
- 精准的行业趋势判断能力  
- 扎实的技术分析功底
- 敏锐的市场情绪感知能力
- 丰富的ETF产品知识
- 严格的风险控制意识

当前服务的投资者类型：{self.investor_style}
"""

    def _build_portfolio_overview(self, portfolio_analysis: Dict) -> str:
        """构建投资组合概览"""
        if not portfolio_analysis:
            return "## 投资组合概览\n暂无组合数据"
        
        total_value = portfolio_analysis.get('总市值', 0)
        total_cost = portfolio_analysis.get('总成本', 0)
        total_return = portfolio_analysis.get('总收益率', '0%')
        etf_count = portfolio_analysis.get('ETF数量', 0)
        
        overview = f"""
## 投资组合概览

- 总市值：{total_value:.2f}元
- 总成本：{total_cost:.2f}元  
- 总收益率：{total_return}
- ETF数量：{etf_count}只
- 分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M')}
"""
        
        # 添加板块分布
        sector_dist = portfolio_analysis.get('板块分布', {})
        if sector_dist:
            overview += "\n### 板块分布：\n"
            for sector, value in sector_dist.items():
                weight = (value / total_value * 100) if total_value > 0 else 0
                overview += f"- {sector}：{value:.2f}元 ({weight:.1f}%)\n"
        
        return overview

    def _build_etf_details(self, etf_data: List[Dict]) -> str:
        """构建ETF详细分析"""
        details = "## ETF详细信息\n\n"
        
        for i, etf in enumerate(etf_data, 1):
            current_price = etf.get('current_price', 0)
            cost_price = etf.get('cost_price', 0)
            high_price = etf.get('high_price', current_price)
            position = etf.get('position', 0)
            
            # 计算收益率
            cost_return = ((current_price - cost_price) / cost_price * 100) if cost_price > 0 else 0
            high_return = ((current_price - high_price) / high_price * 100) if high_price > 0 else 0
            
            details += f"""
### {i}. {etf.get('name', '未知ETF')} ({etf.get('symbol', '')})

**基本信息：**
- 板块：{etf.get('sector', '未知')}
- 风险等级：{etf.get('risk_level', '中')}
- 投资策略：{etf.get('strategy', 'value')}

**价格信息：**
- 当前价格：{current_price:.3f}元
- 成本价格：{cost_price:.3f}元
- 历史高点：{high_price:.3f}元
- 持仓数量：{position}股

**收益情况：**
- 相对成本收益率：{cost_return:.2f}%
- 相对高点回撤：{high_return:.2f}%
- 当前市值：{current_price * position:.2f}元
"""
        
        return details

    def _build_framework_guide(self) -> str:
        """构建分析框架说明"""
        guide = f"""
## 专业投资决策框架（{self.investor_style}）

请按照以下权重进行分析：
"""
        
        for factor, weight in self.decision_framework.items():
            guide += f"- {factor}：{weight}%\n"
        
        guide += """
### 各因子分析要点：

**宏观经济形势（权重说明）：**
- 货币政策走向及流动性环境
- 经济增长预期与通胀水平
- 国际经济环境与地缘政治

**行业主题趋势（权重说明）：**
- 行业景气度与政策支持
- 技术创新与产业升级趋势
- 市场竞争格局变化

**技术面分析（权重说明）：**
- 价格趋势与支撑阻力位
- 成交量变化与资金流向
- 技术指标信号确认

**资金流向（权重说明）：**
- 机构资金配置偏好
- 北向资金流入流出
- ETF申购赎回情况

**基金特性（权重说明）：**
- 跟踪指数质量与偏离度
- 基金规模与流动性
- 费率水平与管理能力

**风险控制（权重说明）：**
- 组合集中度风险
- 市场系统性风险
- 流动性风险评估

**情绪事件（权重说明）：**
- 市场情绪指标
- 突发事件影响
- 投资者行为偏差
"""
        
        return guide

    def _build_analysis_requirements(self) -> str:
        """构建具体分析要求"""
        return f"""
## 分析输出要求

请按照以下结构提供分析报告：

### 1. 综合评分（0-100分）
为每只ETF提供综合评分，并说明评分依据

### 2. 操作建议
- **买入建议**：明确买入理由、建议仓位、目标价位
- **卖出建议**：明确卖出理由、建议减仓比例、止损位
- **持有建议**：持有理由、关注要点、调整条件

### 3. 风险提示
- 主要风险因素识别
- 风险等级评估
- 应对策略建议

### 4. 时间框架
- 短期操作建议（1-3个月）
- 中期配置建议（3-12个月）
- 长期投资建议（1年以上）

### 5. 组合优化建议
- 仓位配置优化
- 板块均衡调整
- 风险分散建议

请确保分析客观、专业，避免过度乐观或悲观的表述。
"""

def generate_professional_prompt(etf_data: List[Dict], portfolio_analysis: Optional[Dict] = None, 
                                investor_style: str = "稳健型") -> str:
    """生成专业投资分析提示词（兼容性函数）"""
    generator = ProfessionalPrompt(investor_style)
    return generator.generate_analysis_prompt(etf_data, portfolio_analysis)
