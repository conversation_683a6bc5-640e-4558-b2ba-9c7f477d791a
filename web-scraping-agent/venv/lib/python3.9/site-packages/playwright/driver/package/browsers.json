{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1091", "installByDefault": true, "browserVersion": "120.0.6099.28"}, {"name": "chromium-with-symbols", "revision": "1091", "installByDefault": false, "browserVersion": "120.0.6099.28"}, {"name": "chromium-tip-of-tree", "revision": "1168", "installByDefault": false, "browserVersion": "121.0.6127.0"}, {"name": "firefox", "revision": "1429", "installByDefault": true, "browserVersion": "119.0"}, {"name": "firefox-asan", "revision": "1429", "installByDefault": false, "browserVersion": "119.0"}, {"name": "firefox-beta", "revision": "1429", "installByDefault": false, "browserVersion": "120.0b8"}, {"name": "webkit", "revision": "1944", "installByDefault": true, "revisionOverrides": {"mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816", "ubuntu18.04-x64": "1728"}, "browserVersion": "17.4"}, {"name": "ffmpeg", "revision": "1009", "installByDefault": true}, {"name": "android", "revision": "1000", "installByDefault": false}]}